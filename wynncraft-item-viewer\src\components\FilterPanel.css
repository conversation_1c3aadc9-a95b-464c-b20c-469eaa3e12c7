.filter-panel {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-right: 2px solid #444;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: width 0.3s ease;
  overflow-y: auto;
  color: #ffffff;
}

.filter-panel.open {
  width: 320px;
}

.filter-panel.closed {
  width: 50px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 2px solid #444;
  background: #333;
  position: sticky;
  top: 0;
  z-index: 1001;
}

.filter-header h2 {
  margin: 0;
  font-size: 1.2em;
  color: #ffff55;
}

.filter-panel.closed .filter-header h2 {
  display: none;
}

.toggle-button {
  background: #555;
  border: none;
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.2em;
  transition: background 0.2s ease;
}

.toggle-button:hover {
  background: #666;
}

.filter-content {
  padding: 16px;
}

.reset-button {
  width: 100%;
  background: #ff5555;
  border: none;
  color: #ffffff;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  margin-bottom: 20px;
  transition: background 0.2s ease;
}

.reset-button:hover {
  background: #ff3333;
}

.filter-section {
  margin-bottom: 24px;
  border-bottom: 1px solid #444;
  padding-bottom: 16px;
}

.filter-section h3 {
  margin: 0 0 12px 0;
  font-size: 1em;
  color: #55ffff;
  border-bottom: 1px solid #555;
  padding-bottom: 4px;
}

.filter-section h4 {
  margin: 8px 0 4px 0;
  font-size: 0.9em;
  color: #ffff55;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9em;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.checkbox-label:hover {
  background: rgba(255, 255, 255, 0.1);
}

.checkbox-label input[type="checkbox"] {
  accent-color: #55ffff;
}

.range-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.range-group label {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 0.9em;
}

.range-group input[type="range"] {
  width: 100%;
  accent-color: #55ffff;
}

.skill-range {
  margin-bottom: 16px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

/* Custom scrollbar */
.filter-panel::-webkit-scrollbar {
  width: 8px;
}

.filter-panel::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.filter-panel::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.filter-panel::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .filter-panel.open {
    width: 100%;
    height: 100vh;
  }
  
  .filter-panel.closed {
    width: 40px;
  }
  
  .toggle-button {
    padding: 6px 10px;
    font-size: 1em;
  }
  
  .filter-content {
    padding: 12px;
  }
  
  .checkbox-label {
    font-size: 0.8em;
  }
}
