import React, { useState, useMemo } from 'react';
import { WynncraftItem } from '../types.js';
import { ItemCard } from './ItemCard.js';
import './ItemGrid.css';

interface ItemGridProps {
  items: (WynncraftItem & { displayName: string })[];
}

type SortOption = 'name' | 'level' | 'rarity' | 'type' | 'dps';
type SortDirection = 'asc' | 'desc';

export const ItemGrid: React.FC<ItemGridProps> = ({ items }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const itemsPerPage = 24;

  const sortedItems = useMemo(() => {
    const sorted = [...items].sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.displayName.toLowerCase();
          bValue = b.displayName.toLowerCase();
          break;
        case 'level':
          aValue = a.requirements.level;
          bValue = b.requirements.level;
          break;
        case 'rarity':
          const rarityOrder = { 'common': 1, 'unique': 2, 'rare': 3, 'legendary': 4, 'fabled': 5, 'mythic': 6, 'set': 7 };
          aValue = rarityOrder[a.rarity as keyof typeof rarityOrder] || 0;
          bValue = rarityOrder[b.rarity as keyof typeof rarityOrder] || 0;
          break;
        case 'type':
          aValue = a.type.toLowerCase();
          bValue = b.type.toLowerCase();
          break;
        case 'dps':
          aValue = a.averageDps || 0;
          bValue = b.averageDps || 0;
          break;
        default:
          aValue = a.displayName.toLowerCase();
          bValue = b.displayName.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return sorted;
  }, [items, sortBy, sortDirection]);

  const totalPages = Math.ceil(sortedItems.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = sortedItems.slice(startIndex, endIndex);

  const handleSortChange = (newSortBy: SortOption) => {
    if (sortBy === newSortBy) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortDirection('asc');
    }
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const renderPagination = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    if (startPage > 1) {
      pages.push(
        <button key={1} onClick={() => handlePageChange(1)} className="page-button">
          1
        </button>
      );
      if (startPage > 2) {
        pages.push(<span key="start-ellipsis" className="ellipsis">...</span>);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`page-button ${i === currentPage ? 'active' : ''}`}
        >
          {i}
        </button>
      );
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(<span key="end-ellipsis" className="ellipsis">...</span>);
      }
      pages.push(
        <button key={totalPages} onClick={() => handlePageChange(totalPages)} className="page-button">
          {totalPages}
        </button>
      );
    }

    return pages;
  };

  if (items.length === 0) {
    return (
      <div className="no-items">
        <h2>No items found</h2>
        <p>Try adjusting your filters or search terms.</p>
      </div>
    );
  }

  return (
    <div className="item-grid-container">
      <div className="grid-controls">
        <div className="sort-controls">
          <span>Sort by:</span>
          {(['name', 'level', 'rarity', 'type', 'dps'] as SortOption[]).map(option => (
            <button
              key={option}
              onClick={() => handleSortChange(option)}
              className={`sort-button ${sortBy === option ? 'active' : ''}`}
            >
              {option.charAt(0).toUpperCase() + option.slice(1)}
              {sortBy === option && (
                <span className="sort-direction">
                  {sortDirection === 'asc' ? ' ↑' : ' ↓'}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      <div className="item-grid">
        {currentItems.map((item, index) => (
          <ItemCard key={`${item.internalName}-${index}`} item={item} />
        ))}
      </div>

      {totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="page-button nav-button"
          >
            Previous
          </button>
          
          {renderPagination()}
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="page-button nav-button"
          >
            Next
          </button>
        </div>
      )}

      <div className="page-info">
        Page {currentPage} of {totalPages} ({items.length} items total)
      </div>
    </div>
  );
};
