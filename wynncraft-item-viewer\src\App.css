.app {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: #ffffff;
}

.main-content {
  flex: 1;
  padding: 20px;
  transition: margin-left 0.3s ease;
  min-height: 100vh;
  overflow-x: hidden;
}

.main-content.with-sidebar {
  margin-left: 320px;
}

.main-content.full-width {
  margin-left: 50px;
}

.app-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 32px 0;
  border-bottom: 2px solid #444;
}

.app-header h1 {
  font-size: 2.5em;
  margin: 0 0 8px 0;
  background: linear-gradient(45deg, #55ffff, #ffff55);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(85, 255, 255, 0.3);
}

.app-header p {
  font-size: 1.1em;
  color: #aaa;
  margin: 0;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: #ffffff;
  text-align: center;
  padding: 32px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #333;
  border-top: 4px solid #55ffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container h2,
.error-container h2 {
  color: #55ffff;
  margin-bottom: 16px;
}

.loading-container p,
.error-container p {
  color: #aaa;
  font-size: 1.1em;
  margin-bottom: 24px;
}

.error-container button {
  background: #ff5555;
  border: none;
  color: #ffffff;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1em;
  font-weight: bold;
  transition: background 0.2s ease;
}

.error-container button:hover {
  background: #ff3333;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .main-content.with-sidebar {
    margin-left: 0;
  }

  .main-content.full-width {
    margin-left: 40px;
  }

  .main-content {
    padding: 16px;
  }

  .app-header {
    padding: 24px 0;
    margin-bottom: 24px;
  }

  .app-header h1 {
    font-size: 2em;
  }

  .app-header p {
    font-size: 1em;
  }
}
