import type { WynncraftItem, FilterState } from '../types.js';

export const filterItems = (items: (WynncraftItem & { displayName: string })[], filters: FilterState): (WynncraftItem & { displayName: string })[] => {
  return items.filter(item => {
    // Search filter
    if (filters.search && !item.displayName.toLowerCase().includes(filters.search.toLowerCase()) && 
        !item.lore?.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }

    // Type filter
    if (filters.type.length > 0 && !filters.type.includes(item.type)) {
      return false;
    }

    // Rarity filter
    if (filters.rarity.length > 0 && !filters.rarity.includes(item.rarity)) {
      return false;
    }

    // Level filter
    if (item.requirements.level < filters.levelMin || item.requirements.level > filters.levelMax) {
      return false;
    }

    // Class requirement filter
    if (filters.classRequirement.length > 0 && item.requirements.classRequirement && 
        !filters.classRequirement.includes(item.requirements.classRequirement)) {
      return false;
    }

    // Skill point filters
    const checkSkillRequirement = (itemReq: number | undefined, min: number, max: number) => {
      if (itemReq === undefined) return min === 0;
      return itemReq >= min && itemReq <= max;
    };

    if (!checkSkillRequirement(item.requirements.strength, filters.strengthMin, filters.strengthMax) ||
        !checkSkillRequirement(item.requirements.dexterity, filters.dexterityMin, filters.dexterityMax) ||
        !checkSkillRequirement(item.requirements.intelligence, filters.intelligenceMin, filters.intelligenceMax) ||
        !checkSkillRequirement(item.requirements.defence, filters.defenceMin, filters.defenceMax) ||
        !checkSkillRequirement(item.requirements.agility, filters.agilityMin, filters.agilityMax)) {
      return false;
    }

    // Identifications filter
    if (filters.hasIdentifications && (!item.identifications || Object.keys(item.identifications).length === 0)) {
      return false;
    }

    // Major IDs filter
    if (filters.hasMajorIds && (!item.majorIds || Object.keys(item.majorIds).length === 0)) {
      return false;
    }

    // Powder slots filter
    if (filters.powderSlots.length > 0 && !filters.powderSlots.includes((item.powderSlots || 0).toString())) {
      return false;
    }

    return true;
  });
};

export const getUniqueValues = (items: (WynncraftItem & { displayName: string })[], key: keyof WynncraftItem): string[] => {
  const values = new Set<string>();
  items.forEach(item => {
    const value = item[key];
    if (value && typeof value === 'string') {
      values.add(value);
    }
  });
  return Array.from(values).sort();
};

export const getUniqueClassRequirements = (items: (WynncraftItem & { displayName: string })[]): string[] => {
  const values = new Set<string>();
  items.forEach(item => {
    if (item.requirements.classRequirement) {
      values.add(item.requirements.classRequirement);
    }
  });
  return Array.from(values).sort();
};

export const getRarityColor = (rarity: string): string => {
  const colors: Record<string, string> = {
    'common': '#ffffff',
    'unique': '#ffff55',
    'rare': '#ff55ff',
    'legendary': '#55ffff',
    'fabled': '#ff5555',
    'mythic': '#aa00aa',
    'set': '#55ff55'
  };
  return colors[rarity.toLowerCase()] || '#ffffff';
};

export const formatDamage = (damage: { min: number; raw: number; max: number } | undefined): string => {
  if (!damage) return 'N/A';
  return `${damage.min}-${damage.max}`;
};

export const formatIdentification = (key: string, value: any): string => {
  if (typeof value === 'object' && value !== null && 'min' in value && 'max' in value) {
    return `${value.min} to ${value.max}`;
  }
  if (typeof value === 'number') {
    return value.toString();
  }
  return String(value);
};
