import React from 'react';
import type { WynncraftItem } from '../types.js';
import { getRarityColor, formatDamage, formatIdentification } from '../utils/filterUtils.js';
import './ItemCard.css';

interface ItemCardProps {
  item: WynncraftItem & { displayName: string };
}

export const ItemCard: React.FC<ItemCardProps> = ({ item }) => {
  const rarityColor = getRarityColor(item.rarity);

  const renderRequirements = () => {
    const reqs = [];
    if (item.requirements.level) reqs.push(`Level: ${item.requirements.level}`);
    if (item.requirements.classRequirement) reqs.push(`Class: ${item.requirements.classRequirement}`);
    if (item.requirements.strength) reqs.push(`Str: ${item.requirements.strength}`);
    if (item.requirements.dexterity) reqs.push(`Dex: ${item.requirements.dexterity}`);
    if (item.requirements.intelligence) reqs.push(`Int: ${item.requirements.intelligence}`);
    if (item.requirements.defence) reqs.push(`Def: ${item.requirements.defence}`);
    if (item.requirements.agility) reqs.push(`Agi: ${item.requirements.agility}`);
    return reqs;
  };

  const renderBaseDamage = () => {
    if (!item.base) return null;
    
    const damages = [];
    if (item.base.baseDamage) damages.push(`Neutral: ${formatDamage(item.base.baseDamage)}`);
    if (item.base.baseEarthDamage) damages.push(`Earth: ${formatDamage(item.base.baseEarthDamage)}`);
    if (item.base.baseThunderDamage) damages.push(`Thunder: ${formatDamage(item.base.baseThunderDamage)}`);
    if (item.base.baseWaterDamage) damages.push(`Water: ${formatDamage(item.base.baseWaterDamage)}`);
    if (item.base.baseFireDamage) damages.push(`Fire: ${formatDamage(item.base.baseFireDamage)}`);
    if (item.base.baseAirDamage) damages.push(`Air: ${formatDamage(item.base.baseAirDamage)}`);
    
    return damages;
  };

  const renderIdentifications = () => {
    if (!item.identifications) return null;
    
    return Object.entries(item.identifications).slice(0, 5).map(([key, value]) => (
      <div key={key} className="identification">
        <span className="id-name">{key}:</span>
        <span className="id-value">{formatIdentification(key, value)}</span>
      </div>
    ));
  };

  return (
    <div className="item-card" style={{ borderColor: rarityColor }}>
      <div className="item-header">
        <h3 className="item-name" style={{ color: rarityColor }}>
          {item.displayName}
        </h3>
        <span className="item-type">{item.type}</span>
        {item.weaponType && <span className="item-subtype">({item.weaponType})</span>}
        {item.armourType && <span className="item-subtype">({item.armourType})</span>}
        {item.accessoryType && <span className="item-subtype">({item.accessoryType})</span>}
      </div>

      <div className="item-rarity" style={{ color: rarityColor }}>
        {item.rarity ? item.rarity.toUpperCase() : 'UNKNOWN'}
      </div>

      {item.lore && (
        <div className="item-lore">
          {item.lore}
        </div>
      )}

      <div className="item-requirements">
        <h4>Requirements:</h4>
        {renderRequirements().map((req, index) => (
          <div key={index} className="requirement">{req}</div>
        ))}
      </div>

      {item.averageDps && (
        <div className="item-dps">
          <strong>Average DPS: {item.averageDps}</strong>
        </div>
      )}

      {item.attackSpeed && (
        <div className="item-attack-speed">
          Attack Speed: {item.attackSpeed}
        </div>
      )}

      {item.powderSlots && (
        <div className="item-powder-slots">
          Powder Slots: {item.powderSlots}
        </div>
      )}

      {renderBaseDamage() && renderBaseDamage()!.length > 0 && (
        <div className="item-base-damage">
          <h4>Base Damage:</h4>
          {renderBaseDamage()!.map((damage, index) => (
            <div key={index} className="base-damage">{damage}</div>
          ))}
        </div>
      )}

      {item.identifications && Object.keys(item.identifications).length > 0 && (
        <div className="item-identifications">
          <h4>Identifications:</h4>
          {renderIdentifications()}
          {Object.keys(item.identifications).length > 5 && (
            <div className="more-ids">+{Object.keys(item.identifications).length - 5} more...</div>
          )}
        </div>
      )}

      {item.majorIds && Object.keys(item.majorIds).length > 0 && (
        <div className="item-major-ids">
          <h4>Major IDs:</h4>
          {Object.entries(item.majorIds).map(([key, value]) => (
            <div key={key} className="major-id">
              <strong>{key}</strong>
            </div>
          ))}
        </div>
      )}

      {item.dropMeta && (
        <div className="item-drop-meta">
          <h4>Drop Info:</h4>
          <div>Source: {item.dropMeta.name}</div>
          <div>Type: {item.dropMeta.type}</div>
        </div>
      )}
    </div>
  );
};
