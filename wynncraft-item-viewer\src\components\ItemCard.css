.item-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border: 2px solid;
  border-radius: 12px;
  padding: 16px;
  margin: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  color: #ffffff;
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.item-header {
  margin-bottom: 12px;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.item-name {
  margin: 0 0 4px 0;
  font-size: 1.2em;
  font-weight: bold;
  text-shadow: 0 0 4px currentColor;
}

.item-type {
  background: #333;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  margin-right: 4px;
}

.item-subtype {
  color: #aaa;
  font-size: 0.8em;
}

.item-rarity {
  font-weight: bold;
  font-size: 0.9em;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.item-lore {
  font-style: italic;
  color: #ccc;
  margin-bottom: 12px;
  font-size: 0.9em;
  line-height: 1.4;
}

.item-requirements h4,
.item-base-damage h4,
.item-identifications h4,
.item-major-ids h4,
.item-drop-meta h4 {
  margin: 8px 0 4px 0;
  font-size: 0.9em;
  color: #ffff55;
  border-bottom: 1px solid #444;
  padding-bottom: 2px;
}

.requirement,
.base-damage {
  font-size: 0.8em;
  color: #ddd;
  margin: 2px 0;
}

.item-dps {
  color: #ff5555;
  font-size: 0.9em;
  margin: 4px 0;
}

.item-attack-speed,
.item-powder-slots {
  font-size: 0.8em;
  color: #aaa;
  margin: 2px 0;
}

.identification {
  display: flex;
  justify-content: space-between;
  font-size: 0.8em;
  margin: 2px 0;
}

.id-name {
  color: #55ffff;
}

.id-value {
  color: #ffffff;
  font-weight: bold;
}

.more-ids {
  font-size: 0.8em;
  color: #888;
  font-style: italic;
  margin-top: 4px;
}

.major-id {
  background: linear-gradient(90deg, #ff5555, #ffaa00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
  font-size: 0.8em;
  margin: 2px 0;
}

.item-drop-meta {
  margin-top: auto;
  padding-top: 8px;
  border-top: 1px solid #444;
}

.item-drop-meta div {
  font-size: 0.8em;
  color: #aaa;
  margin: 1px 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .item-card {
    margin: 4px;
    padding: 12px;
  }
  
  .item-name {
    font-size: 1.1em;
  }
}
