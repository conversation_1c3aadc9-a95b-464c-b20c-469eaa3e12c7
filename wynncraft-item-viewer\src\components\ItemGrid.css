.item-grid-container {
  width: 100%;
}

.grid-controls {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #444;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.sort-controls span {
  color: #ffffff;
  font-weight: bold;
  margin-right: 8px;
}

.sort-button {
  background: #333;
  border: 1px solid #555;
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.sort-button:hover {
  background: #444;
  border-color: #666;
}

.sort-button.active {
  background: #55ffff;
  color: #000000;
  border-color: #55ffff;
}

.sort-direction {
  font-weight: bold;
}

.item-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.no-items {
  text-align: center;
  padding: 64px 32px;
  color: #ffffff;
}

.no-items h2 {
  color: #ff5555;
  margin-bottom: 16px;
}

.no-items p {
  color: #aaa;
  font-size: 1.1em;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin: 32px 0;
  flex-wrap: wrap;
}

.page-button {
  background: #333;
  border: 1px solid #555;
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s ease;
  min-width: 40px;
}

.page-button:hover:not(:disabled) {
  background: #444;
  border-color: #666;
}

.page-button.active {
  background: #55ffff;
  color: #000000;
  border-color: #55ffff;
}

.page-button:disabled {
  background: #222;
  color: #666;
  cursor: not-allowed;
  border-color: #333;
}

.nav-button {
  font-weight: bold;
  padding: 8px 16px;
}

.ellipsis {
  color: #888;
  padding: 8px 4px;
  font-weight: bold;
}

.page-info {
  text-align: center;
  color: #aaa;
  font-size: 0.9em;
  margin-bottom: 32px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .item-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 12px;
  }
  
  .grid-controls {
    padding: 12px;
  }
  
  .sort-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .sort-controls span {
    margin-right: 0;
    margin-bottom: 4px;
  }
  
  .sort-button {
    padding: 6px 10px;
    font-size: 0.8em;
  }
  
  .pagination {
    gap: 4px;
    margin: 24px 0;
  }
  
  .page-button {
    padding: 6px 10px;
    font-size: 0.8em;
    min-width: 36px;
  }
  
  .nav-button {
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .item-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .sort-controls {
    gap: 6px;
  }
  
  .sort-button {
    padding: 4px 8px;
    font-size: 0.7em;
  }
}
