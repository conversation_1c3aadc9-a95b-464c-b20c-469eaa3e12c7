.search-bar-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  font-size: 16px;
  border: 2px solid #444;
  border-radius: 8px;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: #ffffff;
  outline: none;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.search-input:focus {
  border-color: #55ffff;
  box-shadow: 0 0 0 3px rgba(85, 255, 255, 0.1);
}

.search-input::placeholder {
  color: #888;
}

.search-icon {
  position: absolute;
  left: 16px;
  font-size: 18px;
  color: #888;
  pointer-events: none;
}

.search-results-info {
  font-size: 14px;
  color: #aaa;
  text-align: center;
  padding: 4px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .search-input {
    padding: 10px 14px 10px 40px;
    font-size: 14px;
  }
  
  .search-icon {
    left: 12px;
    font-size: 16px;
  }
  
  .search-results-info {
    font-size: 12px;
  }
}
